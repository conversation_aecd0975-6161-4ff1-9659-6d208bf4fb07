// Dashboard Performance Optimizations
// Inspired by medicamentos.js patterns for efficient loading

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard with progressive loading
    initializeDashboard();
});

let dashboardChart = null;
let statsCache = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

function initializeDashboard() {
    // Start loading process
    updateLoadingProgress(10, 'Conectando al servidor...');

    // Load stats with progressive updates
    loadDashboardStats()
        .then(stats => {
            updateLoadingProgress(60, 'Procesando estadísticas...');
            populateStatsCards(stats);
            return stats;
        })
        .then(stats => {
            updateLoadingProgress(80, 'Preparando gráficos...');
            return initializeChart(stats.monthly_stats);
        })
        .then(() => {
            updateLoadingProgress(100, 'Completado');
            setTimeout(() => {
                hideDashboardLoading();
            }, 500);
        })
        .catch(error => {
            console.error('Error loading dashboard:', error);
            showDashboardError(error);
        });
}

function loadDashboardStats() {
    // Check cache first
    if (statsCache && (Date.now() - statsCache.timestamp) < CACHE_DURATION) {
        return Promise.resolve(statsCache.data);
    }

    return fetch(window.BASE_URL + 'dashboard', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error en la respuesta del servidor');
        }
        return response.json();
    })
    .then(result => {
        if (!result.success) {
            throw new Error(result.error || 'Error desconocido');
        }

        // Cache the result
        statsCache = {
            data: result.data,
            timestamp: Date.now()
        };

        return result.data;
    });
}
function populateStatsCards(stats) {
    // Animate numbers with counting effect
    animateNumber('totalUsuarios', stats.total_usuarios);
    animateNumber('totalMedicamentos', stats.total_medicamentos);
}

function animateNumber(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    // Remove placeholder
    element.innerHTML = '';

    let currentValue = 0;
    const increment = Math.ceil(targetValue / 30); // 30 frames animation
    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(timer);
        }
        element.textContent = currentValue.toLocaleString();
    }, 50);
}

function initializeChart(monthlyStats) {
    return new Promise((resolve) => {
        const ctx = document.getElementById('usageChart');
        if (!ctx) {
            resolve();
            return;
        }

        // Use requestAnimationFrame for smooth initialization
        requestAnimationFrame(() => {
            dashboardChart = new Chart(ctx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: monthlyStats.months,
                    datasets: [{
                        label: 'Usuarios Registrados',
                        data: monthlyStats.users,
                        backgroundColor: 'rgba(78, 115, 223, 0.7)',
                        borderColor: 'rgba(78, 115, 223, 1)',
                        borderWidth: 1,
                        borderRadius: 4,
                        barPercentage: 0.8
                    }, {
                        label: 'Medicamentos Agregados',
                        data: monthlyStats.medications,
                        backgroundColor: 'rgba(28, 200, 138, 0.7)',
                        borderColor: 'rgba(28, 200, 138, 1)',
                        borderWidth: 1,
                        borderRadius: 4,
                        barPercentage: 0.8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                drawBorder: false
                            },
                            ticks: {
                                precision: 0,
                                maxTicksLimit: 5
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 13
                            },
                            padding: 12,
                            displayColors: true,
                            usePointStyle: true,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y;
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    animation: {
                        duration: 800, // Reduced from 1000ms
                        easing: 'easeOutQuart'
                    }
                }
            });

            // Setup responsive resize handler
            setupChartResize();
            resolve();
        });
    });
}

function setupChartResize() {
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            if (dashboardChart) {
                dashboardChart.resize();
            }
        }, 250);
    });
}

function updateLoadingProgress(percentage, status) {
    const progressBar = document.getElementById('dashboardProgress');
    const statusText = document.getElementById('loadingStatus');

    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }

    if (statusText) {
        statusText.textContent = status;
    }
}

function hideDashboardLoading() {
    const loadingIndicator = document.getElementById('dashboardLoadingIndicator');
    const dashboardContent = document.getElementById('dashboardContent');

    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }

    if (dashboardContent) {
        dashboardContent.style.display = 'block';
        // Trigger a gentle fade-in effect
        dashboardContent.style.opacity = '0';
        dashboardContent.style.transition = 'opacity 0.3s ease-in';
        setTimeout(() => {
            dashboardContent.style.opacity = '1';
        }, 50);
    }
}

function showDashboardError(error) {
    const loadingIndicator = document.getElementById('dashboardLoadingIndicator');

    if (loadingIndicator) {
        loadingIndicator.innerHTML = `
            <div class="d-flex flex-column align-items-center">
                <div class="text-danger mb-3">
                    <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                </div>
                <h5 class="text-danger mb-2">Error al cargar el Dashboard</h5>
                <p class="text-muted mb-3">${error.message || 'Error desconocido'}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Reintentar
                </button>
            </div>
        `;
    }
}

// Public function to refresh dashboard data
window.refreshDashboard = function() {
    // Clear cache
    statsCache = null;

    // Show loading again
    const loadingIndicator = document.getElementById('dashboardLoadingIndicator');
    const dashboardContent = document.getElementById('dashboardContent');

    if (loadingIndicator && dashboardContent) {
        loadingIndicator.style.display = 'block';
        dashboardContent.style.display = 'none';

        // Restart the loading process
        initializeDashboard();
    }
};

// Cleanup function for page unload
window.addEventListener('beforeunload', function() {
    if (dashboardChart) {
        dashboardChart.destroy();
        dashboardChart = null;
    }
});
