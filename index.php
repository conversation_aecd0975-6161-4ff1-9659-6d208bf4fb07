<?php
session_start();

date_default_timezone_set('America/Lima');

require_once 'config/constants.php';
require_once 'config/auth.php';

require_once 'controllers/AuthController.php';
require_once 'controllers/DashboardController.php';
require_once 'controllers/UsuarioController.php';
require_once 'controllers/MedicamentoController.php';

$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];
$base_path = dirname($script_name);

if ($base_path !== '/') {
    $request_uri = substr($request_uri, strlen($base_path));
}

$request_uri = strtok($request_uri, '?');

$request_uri = ltrim($request_uri, '/');

$segments = explode('/', $request_uri);
$route = $segments[0] ?? '';
$action = $segments[1] ?? 'index';
$id = $segments[2] ?? null;

function requiresAuth($route) {
    global $protected_routes;
    return in_array($route, $protected_routes);
}

function handle404() {
    http_response_code(404);
    echo '<!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Página no encontrada - ' . APP_NAME . '</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6 text-center">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    <h1 class="mt-3">404 - Página no encontrada</h1>
                    <p class="text-muted">La página que buscas no existe.</p>
                    <a href="' . BASE_URL . '" class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>Ir al inicio
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>';
    exit;
}

try {
    switch ($route) {
        case '':
        case 'index':
            if (Auth::isAuthenticated()) {
                header('Location: ' . BASE_URL . 'dashboard');
            } else {
                header('Location: ' . BASE_URL . 'login');
            }
            exit;
            
        case 'login':
            $controller = new AuthController();
            $controller->login();
            break;
            
        case 'logout':
            $controller = new AuthController();
            $controller->logout();
            break;
            
        case 'dashboard':
            $controller = new DashboardController();
            $controller->index();
            break;
            
        case 'usuarios':
            $controller = new UsuarioController();
            
            switch ($action) {
                case 'index':
                case '':
                    $controller->index();
                    break;
                case 'create':
                    $controller->create();
                    break;
                case 'show':
                    if ($id) {
                        $controller->show($id);
                    } else {
                        handle404();
                    }
                    break;
                case 'edit':
                    if ($id) {
                        $controller->edit($id);
                    } else {
                        handle404();
                    }
                    break;
                case 'delete':
                    if ($id) {
                        $controller->delete($id);
                    } else {
                        handle404();
                    }
                    break;
                default:
                    handle404();
            }
            break;
            
        case 'medicamentos':
            $controller = new MedicamentoController();
            
            switch ($action) {
                case 'index':
                case '':
                    $controller->index();
                    break;
                case 'create':
                    $controller->create();
                    break;
                case 'show':
                    if ($id) {
                        $controller->show($id);
                    } else {
                        handle404();
                    }
                    break;
                case 'edit':
                    if ($id) {
                        $controller->edit($id);
                    } else {
                        handle404();
                    }
                    break;
                case 'delete':
                    if ($id) {
                        $controller->delete($id);
                    } else {
                        handle404();
                    }
                    break;
                case 'catalogos':
                    $controller->getCatalogos();
                    break;
                default:
                    handle404();
            }
            break;
            
        default:
            handle404();
    }
    
} catch (Exception $e) {
    error_log("Error en la aplicación: " . $e->getMessage());
    
    http_response_code(500);
    echo '<!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error del servidor - ' . APP_NAME . '</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6 text-center">
                    <i class="bi bi-exclamation-circle text-danger" style="font-size: 4rem;"></i>
                    <h1 class="mt-3">Error del servidor</h1>
                    <p class="text-muted">Ha ocurrido un error interno. Por favor, inténtalo más tarde.</p>
                    <a href="' . BASE_URL . '" class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>Ir al inicio
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>';
}
?>
