<?php

require_once __DIR__ . '/../config/api_client.php';
require_once __DIR__ . '/../config/constants.php';

class Medicamento {
    private $api;

    public $id_medicamento;
    public $nombre;
    public $imagen_url;
    public $id_tipo_medicamento;
    public $id_presentacion_medicamento;
    public $id_unidad_dosis;
    public $dosis_cantidad;
    public $estado_auditoria;
    public $fecha_creacion;

    public function __construct() {
        $this->api = new ApiClient();
    }
    
    public function create() {
        $data = [
            'nombre' => htmlspecialchars(strip_tags($this->nombre)),
            'imagenUrl' => htmlspecialchars(strip_tags($this->imagen_url)),
            'tipoMedicamento' => ['idTipoMedicamento' => $this->id_tipo_medicamento],
            'presentacionMedicamento' => ['idPresentacionMedicamento' => $this->id_presentacion_medicamento],
            'unidadDosis' => ['idUnidadDosis' => $this->id_unidad_dosis],
            'dosis_cantidad' => $this->dosis_cantidad
        ];

        $response = $this->api->post('medicamentos', $data);

        if ($response['success']) {
            return $response['data']['data']['idMedicamento'] ?? true;
        }

        return false;
    }
    
    public function read() {
        $response = $this->api->get('medicamentos');

        if ($response['success']) {
            return $response['data']['data'] ?? [];
        }

        return [];
    }

    public function readPaginated($start = 0, $length = 25, $search = '', $orderBy = 'idMedicamento', $orderDir = 'desc') {
        $allData = $this->read();
        if (!empty($search)) {
            $allData = array_filter($allData, function($item) use ($search) {
                $searchLower = strtolower($search);
                return strpos(strtolower($item['nombre']), $searchLower) !== false ||
                       strpos(strtolower($item['tipoMedicamento']['nombre'] ?? ''), $searchLower) !== false ||
                       strpos(strtolower($item['presentacionMedicamento']['nombre'] ?? ''), $searchLower) !== false ||
                       strpos(strtolower($item['idMedicamento']), $searchLower) !== false;
            });
        }

        $totalRecords = count($this->read());
        $filteredRecords = count($allData);

        usort($allData, function($a, $b) use ($orderBy, $orderDir) {
            $valueA = $a[$orderBy] ?? '';
            $valueB = $b[$orderBy] ?? '';

            if ($orderBy === 'fechaCreacion') {
                $valueA = strtotime($valueA);
                $valueB = strtotime($valueB);
            }

            if ($orderDir === 'asc') {
                return $valueA <=> $valueB;
            } else {
                return $valueB <=> $valueA;
            }
        });

        $paginatedData = array_slice($allData, $start, $length);

        return [
            'data' => $paginatedData,
            'totalRecords' => $totalRecords,
            'filteredRecords' => $filteredRecords
        ];
    }
    
    public function getCountByDateRange($startDate, $endDate) {
        $medicamentos = $this->read();
        $count = 0;
        
        foreach ($medicamentos as $medicamento) {
            $fechaCreacion = $medicamento['fechaCreacion'] ?? null;
            if ($fechaCreacion && $fechaCreacion >= $startDate && $fechaCreacion <= $endDate) {
                $count++;
            }
        }
        
        return $count;
    }
    
    public function readOne() {
        $response = $this->api->get('medicamentos/' . $this->id_medicamento);

        if ($response['success'] && isset($response['data']['data'])) {
            $row = $response['data']['data'];
            $this->nombre = $row['nombre'];
            $this->imagen_url = $row['imagenUrl'];
            $this->id_tipo_medicamento = $row['tipoMedicamento']['idTipoMedicamento'] ?? null;
            $this->id_presentacion_medicamento = $row['presentacionMedicamento']['idPresentacionMedicamento'] ?? null;
            $this->id_unidad_dosis = $row['unidadDosis']['idUnidadDosis'] ?? null;
            $this->dosis_cantidad = $row['dosis_cantidad'] ?? $row['dosisCantidad'] ?? 0;
            $this->estado_auditoria = $row['estadoAuditoria'];
            $this->fecha_creacion = $row['fechaCreacion'];

            return [
                'id_medicamento' => $row['idMedicamento'],
                'nombre' => $row['nombre'],
                'imagen_url' => $row['imagenUrl'],
                'id_tipo_medicamento' => $row['tipoMedicamento']['idTipoMedicamento'] ?? null,
                'id_presentacion_medicamento' => $row['presentacionMedicamento']['idPresentacionMedicamento'] ?? null,
                'id_unidad_dosis' => $row['unidadDosis']['idUnidadDosis'] ?? null,
                'dosis_cantidad' => $row['dosis_cantidad'] ?? $row['dosisCantidad'] ?? 0,
                'estado_auditoria' => $row['estadoAuditoria'],
                'fecha_creacion' => $row['fechaCreacion'],
                'tipo_medicamento_nombre' => $row['tipoMedicamento']['nombre'] ?? null,
                'presentacion_medicamento_nombre' => $row['presentacionMedicamento']['nombre'] ?? null,
                'unidad_dosis_nombre' => $row['unidadDosis']['nombre'] ?? null,
                'unidad_dosis_abreviatura' => $row['unidadDosis']['abreviatura'] ?? null
            ];
        }

        return false;
    }
    
    public function update() {
        $data = [
            'nombre' => htmlspecialchars(strip_tags($this->nombre)),
            'imagenUrl' => htmlspecialchars(strip_tags($this->imagen_url)),
            'tipoMedicamento' => ['idTipoMedicamento' => $this->id_tipo_medicamento],
            'presentacionMedicamento' => ['idPresentacionMedicamento' => $this->id_presentacion_medicamento],
            'unidadDosis' => ['idUnidadDosis' => $this->id_unidad_dosis],
            'dosis_cantidad' => $this->dosis_cantidad
        ];

        $response = $this->api->put('medicamentos/' . $this->id_medicamento, $data);

        return $response['success'];
    }
    
    public function delete() {
        $response = $this->api->delete('medicamentos/' . $this->id_medicamento);

        return $response['success'];
    }

    public function getTiposMedicamento() {
        $response = $this->api->get('tipos-medicamento');

        if ($response['success']) {
            return $response['data']['data'] ?? [];
        }

        return [];
    }

    public function getPresentacionesMedicamento() {
        $response = $this->api->get('presentaciones-medicamento');

        if ($response['success']) {
            return $response['data']['data'] ?? [];
        }

        return [];
    }

    public function getUnidadesDosis() {
        $response = $this->api->get('unidades-dosis');

        if ($response['success']) {
            return $response['data']['data'] ?? [];
        }

        return [];
    }
}

?>
