<?php
$page_title = 'Detalles del Usuario';
$current_page = 'usuarios';
$page_actions = '<a href="' . BASE_URL . 'usuarios" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Volver</a>';

ob_start();
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-person me-2"></i>
                    Información del Usuario
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-person me-1"></i>
                            Nombre Completo:
                        </label>
                        <p class="form-control-plaintext">
                            <?php echo htmlspecialchars($this->usuario->nombre . ' ' . $this->usuario->apellido_paterno . ' ' . $this->usuario->apellido_materno); ?>
                        </p>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-envelope me-1"></i>
                            Correo Electrónico:
                        </label>
                        <p class="form-control-plaintext">
                            <a href="mailto:<?php echo htmlspecialchars($this->usuario->correo); ?>">
                                <?php echo htmlspecialchars($this->usuario->correo); ?>
                            </a>
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-telephone me-1"></i>
                            Teléfono:
                        </label>
                        <p class="form-control-plaintext">
                            <?php if (!empty($this->usuario->telefono)): ?>
                                <a href="tel:<?php echo htmlspecialchars($this->usuario->telefono); ?>">
                                    <?php echo htmlspecialchars($this->usuario->telefono); ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">No especificado</span>
                            <?php endif; ?>
                        </p>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-calendar me-1"></i>
                            Fecha de Nacimiento:
                        </label>
                        <p class="form-control-plaintext">
                            <?php
                            $fecha_nacimiento = new DateTime($this->usuario->fecha_nacimiento);
                            $hoy = new DateTime();
                            $edad = $hoy->diff($fecha_nacimiento)->y;
                            echo $fecha_nacimiento->format('d/m/Y') . ' (' . $edad . ' años)';
                            ?>
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-check-circle me-1"></i>
                            Estado:
                        </label>
                        <p class="form-control-plaintext">
                            <?php if ($this->usuario->estado_auditoria == ESTADO_ACTIVO): ?>
                                <span class="badge bg-success">Activo</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Inactivo</span>
                            <?php endif; ?>
                        </p>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-clock me-1"></i>
                            Fecha de Registro:
                        </label>
                        <p class="form-control-plaintext">
                            <?php echo date('d/m/Y H:i:s', strtotime($this->usuario->fecha_creacion)); ?>
                        </p>
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <a href="<?php echo BASE_URL; ?>usuarios" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        Volver a la lista
                    </a>
                    <div>
                        <a href="<?php echo BASE_URL; ?>usuarios/edit/<?php echo $this->usuario->id_usuario; ?>"
                            class="btn btn-warning me-2">
                            <i class="bi bi-pencil me-2"></i>
                            Editar
                        </a>
                        <a href="javascript:void(0);"
                            class="btn btn-danger"
                            onclick="confirmDeleteLink('<?php echo BASE_URL; ?>usuarios/delete/<?php echo $this->usuario->id_usuario; ?>')">
                            <i class="bi bi-trash me-2"></i>
                            Eliminar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>