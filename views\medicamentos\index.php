<?php
$page_title = 'Gestión de Medicamentos';
$current_page = 'medicamentos';
$page_actions = '<a href="' . BASE_URL . 'medicamentos/create" class="btn btn-primary"><i class="bi bi-plus-circle me-2"></i>Nuevo Medicamento</a>';

ob_start();
?>

<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-capsule me-2"></i>
            Lista de Medicamentos
        </h6>
    </div>
    <div class="card-body p-0">
        <div id="loadingIndicator" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <h5 class="text-primary mb-2">
                    <i class="bi bi-capsule me-2"></i>
                    Cargando Medicamentos
                </h5>
                <p class="text-muted mb-3">Preparando la tabla con 1491 registros...</p>
                <div class="progress" style="width: 300px; height: 8px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: 100%"></div>
                </div>
                <small class="text-muted mt-2">
                    <i class="bi bi-info-circle me-1"></i>
                    Esto puede tomar unos segundos
                </small>
            </div>
        </div>

        <div id="searchLoadingIndicator" class="text-center py-3" style="display: none;">
            <div class="d-flex align-items-center justify-content-center">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                    <span class="visually-hidden">Buscando...</span>
                </div>
                <span class="text-primary">
                    <i class="bi bi-search me-1"></i>
                    Buscando medicamentos...
                </span>
            </div>
        </div>

        <div class="table-responsive p-3" style="display: none;" id="tableContainer">
            <table class="table table-bordered table-hover w-100" id="medicamentosTable">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Tipo</th>
                        <th>Presentación</th>
                        <th>Dosis</th>
                        <th>Imagen</th>
                        <th>Fecha Creación</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Los datos se cargarán via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$additional_js = '<script src="' . BASE_URL . 'assets/js/medicamentos.js"></script>';

$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>
