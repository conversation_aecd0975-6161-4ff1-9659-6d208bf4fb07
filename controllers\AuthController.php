<?php

require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../config/functions.php';
require_once __DIR__ . '/../models/Usuario.php';

class AuthController {
    private $usuario;

    public function __construct() {
        $this->usuario = new Usuario();
        
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if ($email === ADMIN_EMAIL && $password === ADMIN_PASSWORD) {
                $user_data = [
                    'id' => 0,
                    'email' => ADMIN_EMAIL,
                    'nombre' => 'Administrador',
                    'tipo' => 'admin'
                ];
                
                $token = Auth::generateToken($user_data);
                
                $_SESSION['auth_token'] = $token;
                $_SESSION['user_data'] = $user_data;
                
                $_SESSION['flash_messages']['success'] = 'Bienvenido al sistema de gestión de MediRemind. Desde aquí puedes administrar usuarios y medicamentos.';
                
                if ($this->isAjaxRequest()) {
                    echo json_encode([
                        'success' => true,
                        'message' => MSG_LOGIN_SUCCESS,
                        'token' => $token,
                        'user' => $user_data,
                        'redirect' => BASE_URL . 'dashboard'
                    ]);
                } else {
                    header('Location: ' . BASE_URL . 'dashboard');
                }
                return;
            }
            
            $user = $this->usuario->authenticateUser($email, $password);

            if ($user) {
                $user_data = [
                    'id' => $user['idUsuario'],
                    'email' => $user['correo'],
                    'nombre' => $user['nombre'] . ' ' . $user['apellidoPaterno'],
                    'tipo' => 'user'
                ];

                $token = Auth::generateToken($user_data);
                $_SESSION['auth_token'] = $token;
                $_SESSION['user_data'] = $user_data;
                
                $_SESSION['flash_messages']['success'] = 'Bienvenido al sistema de gestión de MediRemind. Desde aquí puedes administrar usuarios y medicamentos.';

                if ($this->isAjaxRequest()) {
                    echo json_encode([
                        'success' => true,
                        'message' => MSG_LOGIN_SUCCESS,
                        'token' => $token,
                        'user' => $user_data,
                        'redirect' => BASE_URL . 'dashboard'
                    ]);
                } else {
                    header('Location: ' . BASE_URL . 'dashboard');
                }
            } else {
                if ($this->isAjaxRequest()) {
                    echo json_encode([
                        'success' => false,
                        'message' => MSG_LOGIN_ERROR
                    ]);
                } else {
                    header('Location: ' . BASE_URL . 'login?error=1');
                }
            }
        } else {
            include __DIR__ . '/../views/auth/login.php';
        }
    }
    
    public function logout() {
        Auth::logout();
    }
    
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

?>
