document.addEventListener("DOMContentLoaded", function() {
    if (window.jQuery && $.fn.DataTable) {
        function hideLoadingIndicator() {
            $("#loadingIndicator").hide();
            $("#tableContainer").show();
        }

        $("#medicamentosTable").DataTable({
            processing: true,
            serverSide: true,
            deferRender: true,
            stateSave: true,
            stateDuration: 60 * 60 * 24,
            ajax: {
                url: window.BASE_URL + "medicamentos",
                type: "GET",
                data: function(d) {
                    d.ajax = 1;
                },
                error: function(xhr, error, thrown) {
                    console.error("Error al cargar datos:", error);
                    hideLoadingIndicator();
                    alert("Error al cargar los datos. Por favor, recargue la página.");
                }
            },
            language: {
                url: "https://cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json",
                processing: "Procesando...",
                loadingRecords: "Cargando...",
                emptyTable: "No hay medicamentos registrados"
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
            order: [[0, "desc"]],
            autoWidth: false,
            scrollX: true,
            columns: [
                { data: 0, title: "ID", width: "8%", className: "text-center" },
                { data: 1, title: "Nombre", width: "20%" },
                { data: 2, title: "Tipo", width: "15%", className: "text-center" },
                { data: 3, title: "Presentación", width: "15%", className: "text-center" },
                { data: 4, title: "Dosis", width: "12%", className: "text-center" },
                { data: 5, title: "Imagen", width: "10%", orderable: false, className: "text-center" },
                { data: 6, title: "Fecha Creación", width: "12%", className: "text-center" },
                { data: 7, title: "Acciones", width: "8%", orderable: false, className: "text-center" }
            ],
            dom: "<\"row\"<\"col-sm-12 col-md-6\"l><\"col-sm-12 col-md-6\"f>>" +
                 "<\"row\"<\"col-sm-12\"tr>>" +
                 "<\"row\"<\"col-sm-12 col-md-5\"i><\"col-sm-12 col-md-7\"p>>",
            initComplete: function(settings, json) {
                hideLoadingIndicator();
                $("#medicamentosTable").css("width", "100%");
                this.api().columns.adjust().draw();
            },
            drawCallback: function(settings) {
                if (typeof bootstrap !== "undefined" && bootstrap.Tooltip) {
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[title]"));
                    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }
            }
        });
    } else {
        console.warn("DataTables o jQuery no están cargados correctamente.");
        hideLoadingIndicator();
    }

    window.refreshMedicamentosTable = function() {
        if ($.fn.DataTable.isDataTable("#medicamentosTable")) {
            $("#medicamentosTable").DataTable().ajax.reload();
        }
    };
});
