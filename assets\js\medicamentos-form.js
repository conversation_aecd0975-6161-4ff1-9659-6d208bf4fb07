document.addEventListener("DOMContentLoaded", function() {
    const imageUrlInput = document.getElementById("imagen_url");
    
    if (imageUrlInput) {
        imageUrlInput.addEventListener("input", function() {
            const url = this.value;
            const preview = document.getElementById("imagePreview");
            const img = document.getElementById("previewImg");
            
            if (url && isValidUrl(url)) {
                img.src = url;
                img.onload = function() {
                    preview.style.display = "block";
                };
                img.onerror = function() {
                    preview.style.display = "none";
                };
            } else {
                preview.style.display = "none";
            }
        });
    }
});

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

document.addEventListener("DOMContentLoaded", function() {
    const createForm = document.getElementById("createMedicamentoForm");
    
    if (createForm) {
        createForm.addEventListener("submit", function(e) {
            const nombre = document.getElementById("nombre").value.trim();
            
            if (!nombre) {
                e.preventDefault();
                alert("El nombre del medicamento es obligatorio");
                return false;
            }
            
            const submitBtn = this.querySelector("button[type=\"submit\"]");
            submitBtn.innerHTML = "<i class=\"bi bi-hourglass-split me-2\"></i>Creando...";
            submitBtn.disabled = true;
        });
    }
});

document.addEventListener("DOMContentLoaded", function() {
    const editForm = document.getElementById("editMedicamentoForm");
    
    if (editForm) {
        editForm.addEventListener("submit", function(e) {
            const nombre = document.getElementById("nombre").value.trim();
            
            if (!nombre) {
                e.preventDefault();
                alert("El nombre del medicamento es obligatorio");
                return false;
            }
            
            const submitBtn = this.querySelector("button[type=\"submit\"]");
            submitBtn.innerHTML = "<i class=\"bi bi-hourglass-split me-2\"></i>Actualizando...";
            submitBtn.disabled = true;
        });
    }
});
