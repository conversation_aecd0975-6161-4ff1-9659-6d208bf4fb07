<?php
require_once 'config/api_client.php';

$api = new ApiClient();

echo "<h1>Test de Conexión a la API</h1>";

// Verificar si la API está disponible
echo "<h2>Estado de la API</h2>";
if ($api->isApiAvailable()) {
    echo "<p style='color: green;'>✅ API disponible</p>";
} else {
    echo "<p style='color: red;'>❌ API no disponible</p>";
}

// Información de la API
$info = $api->getApiInfo();
echo "<h2>Información de la API</h2>";
echo "<ul>";
echo "<li><strong>URL Base:</strong> " . $info['base_url'] . "</li>";
echo "<li><strong>Timeout:</strong> " . $info['timeout'] . " segundos</li>";
echo "<li><strong>Disponible:</strong> " . ($info['available'] ? 'Sí' : 'No') . "</li>";
echo "</ul>";

// Test de endpoints
echo "<h2>Test de Endpoints</h2>";

// Test usuarios
echo "<h3>Usuarios</h3>";
$response = $api->get('usuarios');
echo "<p><strong>GET /usuarios:</strong> ";
if ($response['success']) {
    echo "<span style='color: green;'>✅ Exitoso</span>";
    echo " - " . count($response['data']['data'] ?? []) . " usuarios encontrados";
} else {
    echo "<span style='color: red;'>❌ Error: " . $response['error'] . "</span>";
}
echo "</p>";

// Test medicamentos
echo "<h3>Medicamentos</h3>";
$response = $api->get('medicamentos');
echo "<p><strong>GET /medicamentos:</strong> ";
if ($response['success']) {
    echo "<span style='color: green;'>✅ Exitoso</span>";
    echo " - " . count($response['data']['data'] ?? []) . " medicamentos encontrados";
} else {
    echo "<span style='color: red;'>❌ Error: " . $response['error'] . "</span>";
}
echo "</p>";

// Test tipos de medicamento
echo "<h3>Tipos de Medicamento</h3>";
$response = $api->get('tipos-medicamento');
echo "<p><strong>GET /tipos-medicamento:</strong> ";
if ($response['success']) {
    echo "<span style='color: green;'>✅ Exitoso</span>";
    echo " - " . count($response['data']['data'] ?? []) . " tipos encontrados";
} else {
    echo "<span style='color: red;'>❌ Error: " . $response['error'] . "</span>";
}
echo "</p>";

// Test presentaciones de medicamento
echo "<h3>Presentaciones de Medicamento</h3>";
$response = $api->get('presentaciones-medicamento');
echo "<p><strong>GET /presentaciones-medicamento:</strong> ";
if ($response['success']) {
    echo "<span style='color: green;'>✅ Exitoso</span>";
    echo " - " . count($response['data']['data'] ?? []) . " presentaciones encontradas";
} else {
    echo "<span style='color: red;'>❌ Error: " . $response['error'] . "</span>";
}
echo "</p>";

// Test unidades de dosis
echo "<h3>Unidades de Dosis</h3>";
$response = $api->get('unidades-dosis');
echo "<p><strong>GET /unidades-dosis:</strong> ";
if ($response['success']) {
    echo "<span style='color: green;'>✅ Exitoso</span>";
    echo " - " . count($response['data']['data'] ?? []) . " unidades encontradas";
} else {
    echo "<span style='color: red;'>❌ Error: " . $response['error'] . "</span>";
}
echo "</p>";

echo "<hr>";
echo "<p><a href='index.php'>← Volver al backoffice</a></p>";
?>
