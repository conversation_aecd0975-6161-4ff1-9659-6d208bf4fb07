<?php

require_once 'constants.php';

class ApiClient {
    private $base_url;
    private $timeout;
    
    public function __construct() {
        $this->base_url = API_BASE_URL;
        $this->timeout = API_TIMEOUT;
    }
    
    public function get($endpoint, $headers = []) {
        return $this->makeRequest('GET', $endpoint, null, $headers);
    }
    
    public function post($endpoint, $data = null, $headers = []) {
        return $this->makeRequest('POST', $endpoint, $data, $headers);
    }
    
    public function put($endpoint, $data = null, $headers = []) {
        return $this->makeRequest('PUT', $endpoint, $data, $headers);
    }
    
    public function delete($endpoint, $headers = []) {
        return $this->makeRequest('DELETE', $endpoint, null, $headers);
    }

    private function makeRequest($method, $endpoint, $data = null, $headers = []) {
        $url = $this->base_url . ltrim($endpoint, '/');
        
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => array_merge([
                'Content-Type: application/json',
                'Accept: application/json'
            ], $headers),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'error' => 'Error de conexión: ' . $error,
                'http_code' => 0
            ];
        }
        
        $decoded_response = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'error' => 'Respuesta inválida de la API',
                'http_code' => $http_code,
                'raw_response' => $response
            ];
        }
        
        $success = $http_code >= 200 && $http_code < 300;
        
        return [
            'success' => $success,
            'data' => $decoded_response,
            'http_code' => $http_code,
            'error' => $success ? null : ($decoded_response['message'] ?? 'Error desconocido')
        ];
    }
    
    public function isApiAvailable() {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->base_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_NOBODY => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        return !$error && $http_code > 0;
    }
    
    public function getApiInfo() {
        return [
            'base_url' => $this->base_url,
            'timeout' => $this->timeout,
            'available' => $this->isApiAvailable()
        ];
    }
}

?>
