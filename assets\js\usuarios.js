document.addEventListener("DOMContentLoaded", function() {
    if (window.jQuery && $.fn.DataTable) {
        function hideLoadingIndicator() {
            $("#loadingIndicator").hide();
            $("#tableContainer").show();
        }

        $("#usuariosTable").DataTable({
            ajax: {
                url: window.BASE_URL + "usuarios",
                type: "GET",
                headers: {
                    "X-Requested-With": "XMLHttpRequest"
                },
                dataSrc: function(json) {
                    if (json.success && json.data) {
                        return json.data;
                    }
                    return [];
                },
                error: function(xhr, error, thrown) {
                    console.error("Error al cargar datos:", error);
                    hideLoadingIndicator();
                    alert("Error al cargar los datos. Por favor, recargue la página.");
                }
            },
            columns: [
                { 
                    data: "idUsuario",
                    title: "ID",
                    className: "text-center"
                },
                { 
                    data: null,
                    title: "Nombre Completo",
                    render: function(data, type, row) {
                        return "<strong>" + (row.nombre + " " + row.apellidoPaterno + " " + row.apellidoMaterno) + "</strong>";
                    }
                },
                { 
                    data: "correo",
                    title: "Correo",
                    render: function(data, type, row) {
                        return "<i class=\"bi bi-envelope me-1\"></i>" + data;
                    }
                },
                { 
                    data: "telefono",
                    title: "Teléfono",
                    render: function(data, type, row) {
                        if (data && data.trim() !== "") {
                            return "<i class=\"bi bi-telephone me-1\"></i>" + data;
                        } else {
                            return "<span class=\"text-muted\">No especificado</span>";
                        }
                    }
                },
                { 
                    data: "fechaNacimiento",
                    title: "Fecha Nacimiento",
                    render: function(data, type, row) {
                        if (data) {
                            var date = new Date(data);
                            return "<i class=\"bi bi-calendar me-1\"></i>" + date.toLocaleDateString("es-ES");
                        }
                        return "<span class=\"text-muted\">No especificado</span>";
                    }
                },
                { 
                    data: "fechaCreacion",
                    title: "Fecha Creación",
                    render: function(data, type, row) {
                        if (data) {
                            var date = new Date(data);
                            return "<small class=\"text-muted\">" + date.toLocaleDateString("es-ES") + " " + date.toLocaleTimeString("es-ES", {hour: "2-digit", minute: "2-digit"}) + "</small>";
                        }
                        return "<span class=\"text-muted\">No especificado</span>";
                    }
                },
                { 
                    data: null,
                    title: "Acciones",
                    orderable: false,
                    className: "text-center",
                    render: function(data, type, row) {
                        return `
                            <div class="btn-group" role="group">
                                <a href="${window.BASE_URL}usuarios/show/${row.idUsuario}" class="btn btn-sm btn-outline-info" title="Ver detalles">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="${window.BASE_URL}usuarios/edit/${row.idUsuario}" class="btn btn-sm btn-outline-warning" title="Editar">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="javascript:void(0);" class="btn btn-sm btn-outline-danger" title="Eliminar"
                                onclick="confirmDeleteLink('${window.BASE_URL}usuarios/delete/${row.idUsuario}')">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        `;
                    }
                }
            ],
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json",
                processing: "Procesando...",
                loadingRecords: "Cargando...",
                emptyTable: "No hay usuarios registrados"
            },
            responsive: true,
            pageLength: 25,
            order: [[0, "desc"]],
            initComplete: function(settings, json) {
                hideLoadingIndicator();
            },
            drawCallback: function(settings) {
                if (typeof bootstrap !== "undefined" && bootstrap.Tooltip) {
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[title]"));
                    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }
            }
        });
    } else {
        console.warn("DataTables o jQuery no están cargados correctamente.");
        hideLoadingIndicator();
    }
    
    window.refreshUsuariosTable = function() {
        if ($.fn.DataTable.isDataTable("#usuariosTable")) {
            $("#usuariosTable").DataTable().ajax.reload();
        }
    };
});
