<?php

// Configuración general
define('APP_NAME', 'MediRemind Backoffice');
define('APP_VERSION', '1.6.9');

// Define BASE_URL dinámicamente para funcionar en diferentes entornos de desarrollo.
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') || (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://"; // Protocolo (http o https).
$host = $_SERVER['HTTP_HOST'] ?? 'localhost'; // Host (ej.: localhost:8000 o localhost).
$path = rtrim(dirname($_SERVER['SCRIPT_NAME'] ?? ''), '/\\') . '/'; // Ruta base (ej. / o /am-ea2-backoffice/).
define('BASE_URL', $protocol . $host . $path);

// Configuración de la API
define('API_BASE_URL', 'http://localhost:3000/api/v1/');
define('API_TIMEOUT', 30); // segundos

// Configuración de JWT
define('JWT_SECRET', 'mediremind_secret_key_2025');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRATION', 3600); // 1 hora

// Estados de auditoría
define('ESTADO_ACTIVO', 1);
define('ESTADO_INACTIVO', 0);

// Mensajes del sistema
define('MSG_LOGIN_SUCCESS', 'Inicio de sesión exitoso');
define('MSG_LOGIN_ERROR', 'Credenciales incorrectas');
define('MSG_ACCESS_DENIED', 'Acceso denegado');
define('MSG_SESSION_EXPIRED', 'Sesión expirada');
define('MSG_CREATED_SUCCESS', 'Registro creado exitosamente');
define('MSG_UPDATED_SUCCESS', 'Registro actualizado exitosamente');
define('MSG_DELETED_SUCCESS', 'Registro eliminado exitosamente');
define('MSG_ERROR_GENERAL', 'Ha ocurrido un error');

// Rutas protegidas
$protected_routes = [
    'dashboard',
    'usuarios',
    'medicamentos',
    'logout'
];

// Usuario administrador por defecto
define('ADMIN_EMAIL', '<EMAIL>');
define('ADMIN_PASSWORD', 'admin');

?>
