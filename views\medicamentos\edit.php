<?php
$page_title = 'Editar Medicamento';
$current_page = 'medicamentos';
$page_actions = '<a href="' . BASE_URL . 'medicamentos" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Volver</a>';

ob_start();
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-pencil me-2"></i>
                    Editar Medicamento
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo BASE_URL; ?>medicamentos/edit/<?php echo $medicamento_data['id_medicamento']; ?>" id="editMedicamentoForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nombre" class="form-label">
                                <i class="bi bi-capsule me-1"></i>
                                Nombre del Medicamento <span class="text-danger">*</span>
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="nombre"
                                   name="nombre"
                                   value="<?php echo htmlspecialchars($medicamento_data['nombre']); ?>"
                                   required
                                   maxlength="100">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="imagen_url" class="form-label">
                                <i class="bi bi-image me-1"></i>
                                URL de la Imagen
                            </label>
                            <input type="url"
                                   class="form-control"
                                   id="imagen_url"
                                   name="imagen_url"
                                   value="<?php echo htmlspecialchars($medicamento_data['imagen_url']); ?>"
                                   maxlength="200"
                                   placeholder="https://ejemplo.com/imagen.jpg">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="id_tipo_medicamento" class="form-label">
                                <i class="bi bi-tags me-1"></i>
                                Tipo de Medicamento
                            </label>
                            <select class="form-select" id="id_tipo_medicamento" name="id_tipo_medicamento">
                                <option value="">Seleccionar tipo...</option>
                                <?php if (!empty($tipos_medicamento)): ?>
                                    <?php foreach ($tipos_medicamento as $tipo): ?>
                                        <option value="<?php echo $tipo['idTipoMedicamento']; ?>"
                                                <?php echo ($medicamento_data['id_tipo_medicamento'] == $tipo['idTipoMedicamento']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($tipo['nombre']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="id_presentacion_medicamento" class="form-label">
                                <i class="bi bi-box me-1"></i>
                                Presentación
                            </label>
                            <select class="form-select" id="id_presentacion_medicamento" name="id_presentacion_medicamento">
                                <option value="">Seleccionar presentación...</option>
                                <?php if (!empty($presentaciones_medicamento)): ?>
                                    <?php foreach ($presentaciones_medicamento as $presentacion): ?>
                                        <option value="<?php echo $presentacion['idPresentacionMedicamento']; ?>"
                                                <?php echo ($medicamento_data['id_presentacion_medicamento'] == $presentacion['idPresentacionMedicamento']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($presentacion['nombre']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="id_unidad_dosis" class="form-label">
                                <i class="bi bi-rulers me-1"></i>
                                Unidad de Dosis
                            </label>
                            <select class="form-select" id="id_unidad_dosis" name="id_unidad_dosis">
                                <option value="">Seleccionar unidad...</option>
                                <?php if (!empty($unidades_dosis)): ?>
                                    <?php foreach ($unidades_dosis as $unidad): ?>
                                        <option value="<?php echo $unidad['idUnidadDosis']; ?>"
                                                <?php echo ($medicamento_data['id_unidad_dosis'] == $unidad['idUnidadDosis']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($unidad['nombre'] . ' (' . $unidad['abreviatura'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="dosis_cantidad" class="form-label">
                                <i class="bi bi-calculator me-1"></i>
                                Cantidad de Dosis
                            </label>
                            <input type="number"
                                   class="form-control"
                                   id="dosis_cantidad"
                                   name="dosis_cantidad"
                                   value="<?php echo htmlspecialchars($medicamento_data['dosis_cantidad']); ?>"
                                   min="0"
                                   step="0.01"
                                   placeholder="0">
                        </div>
                    </div>
                    
                    <div class="row" id="imagePreview" style="<?php echo !empty($medicamento_data['imagen_url']) ? 'display: block;' : 'display: none;'; ?>">
                        <div class="col-12 mb-3">
                            <label class="form-label">Vista previa de la imagen:</label>
                            <div class="text-center">
                                <img id="previewImg"
                                     src="<?php echo htmlspecialchars($medicamento_data['imagen_url']); ?>"
                                     alt="Vista previa"
                                     class="img-thumbnail"
                                     style="max-width: 200px; max-height: 200px;">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo BASE_URL; ?>medicamentos" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            Actualizar Medicamento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$additional_js = '<script src="' . BASE_URL . 'assets/js/medicamentos-form.js"></script>';

$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>
