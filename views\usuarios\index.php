<?php
$page_title = 'Gestión de Usuarios';
$current_page = 'usuarios';
$page_actions = '<a href="' . BASE_URL . 'usuarios/create" class="btn btn-primary"><i class="bi bi-person-plus me-2"></i>Nuevo Usuario</a>';

ob_start();
?>

<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-people me-2"></i>
            Lista de Usuarios
        </h6>
    </div>
    <div class="card-body p-0">
        <div id="loadingIndicator" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <h5 class="text-primary mb-2">
                    <i class="bi bi-people me-2"></i>
                    Cargando Usuarios
                </h5>
                <p class="text-muted mb-3">Preparando la tabla de usuarios...</p>
                <div class="progress" style="width: 300px; height: 8px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: 100%"></div>
                </div>
                <small class="text-muted mt-2">
                    <i class="bi bi-info-circle me-1"></i>
                    Esto puede tomar unos segundos
                </small>
            </div>
        </div>

        <div class="table-responsive p-3" style="display: none;" id="tableContainer">
            <table class="table table-bordered table-hover w-100" id="usuariosTable">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nombre Completo</th>
                        <th>Correo</th>
                        <th>Teléfono</th>
                        <th>Fecha Nacimiento</th>
                        <th>Fecha Creación</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Los datos se cargarán via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$additional_js = '<script src="' . BASE_URL . 'assets/js/usuarios.js"></script>';

$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>
