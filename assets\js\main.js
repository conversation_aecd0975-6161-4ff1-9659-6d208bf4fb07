setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    });
}, 5000);

function confirmDeleteLink(url) {
    Swal.fire({
        title: '¿Está seguro?',
        text: 'Esta acción no se puede deshacer.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = url;
        }
    });
}

document.addEventListener('DOMContentLoaded', function () {
    const flashEncoded = sessionStorage.getItem('flash_message');
    if (flashEncoded) {
        try {
            const decoded = JSON.parse(atob(flashEncoded));
            if (decoded && decoded.type && decoded.text) {
                Swal.fire({
                    icon: decoded.type,
                    title: decoded.type === 'success' ? 'Éxito' : 'Error',
                    text: decoded.text,
                    confirmButtonText: 'Aceptar'
                });
            }
        } catch (e) {
            console.error('Error al procesar flash_message', e);
        }

        sessionStorage.removeItem('flash_message');
    }
});

function makeAjaxRequest(url, method = 'GET', data = null) {
    return fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: data ? JSON.stringify(data) : null
    }).then(response => response.json());
}
