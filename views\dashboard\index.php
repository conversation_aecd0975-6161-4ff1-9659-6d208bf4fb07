<?php
$page_title = 'Dashboard';
$current_page = 'dashboard';

ob_start();
?>

<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Usuarios
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['total_usuarios']; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fs-2 text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Medicamentos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['total_medicamentos']; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-capsule fs-2 text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Sistema
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            Activo
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fs-2 text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Versión
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo APP_VERSION; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-gear fs-2 text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning me-2"></i>
                    Accesos Rápidos
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="<?php echo BASE_URL; ?>usuarios/create" class="btn btn-primary w-100">
                            <i class="bi bi-person-plus me-2"></i>
                            Nuevo Usuario
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="<?php echo BASE_URL; ?>medicamentos/create" class="btn btn-success w-100">
                            <i class="bi bi-plus-circle me-2"></i>
                            Nuevo Medicamento
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?php echo BASE_URL; ?>usuarios" class="btn btn-outline-primary w-100">
                            <i class="bi bi-people me-2"></i>
                            Ver Usuarios
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?php echo BASE_URL; ?>medicamentos" class="btn btn-outline-success w-100">
                            <i class="bi bi-capsule me-2"></i>
                            Ver Medicamentos
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-info-circle me-2"></i>
                    Información del Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Aplicación:</strong> <?php echo APP_NAME; ?>
                </div>
                <div class="mb-3">
                    <strong>Versión:</strong> <?php echo APP_VERSION; ?>
                </div>
                <div class="mb-3">
                    <strong>Usuario actual:</strong> 
                    <?php echo htmlspecialchars($_SESSION['user_data']['nombre']); ?>
                </div>
                <div class="mb-3">
                    <strong>Tipo de usuario:</strong> 
                    <span class="badge bg-primary">
                        <?php echo ucfirst($_SESSION['user_data']['tipo']); ?>
                    </span>
                </div>
                <div>
                    <strong>Última conexión:</strong> 
                    <?php echo date('d/m/Y H:i:s'); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 0.5rem;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}
</style>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bar-chart-line me-2"></i>
                    Estadísticas de Uso - Año <?php echo date('Y'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="usageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    window.monthlyStatsData = <?php echo json_encode($stats['monthly_stats']); ?>;
</script>
<script src="<?php echo BASE_URL; ?>assets/js/dashboard.js"></script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>
