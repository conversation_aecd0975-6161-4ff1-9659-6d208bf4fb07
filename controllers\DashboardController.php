<?php

require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../models/Usuario.php';
require_once __DIR__ . '/../models/Medicamento.php';

class DashboardController {
    
    public function index() {
        $user = Auth::requireAuth();
        
        $stats = $this->getStats();
        
        include __DIR__ . '/../views/dashboard/index.php';
    }
    
    private function getStats() {
        try {
            $usuario = new Usuario();
            $medicamento = new Medicamento();

            $usuarios = $usuario->read();
            $total_usuarios = count($usuarios);

            $medicamentos = $medicamento->read();
            $total_medicamentos = count($medicamentos);

            $monthlyStats = $this->getMonthlyStats();

            return [
                'total_usuarios' => $total_usuarios,
                'total_medicamentos' => $total_medicamentos,
                'monthly_stats' => $monthlyStats
            ];
        } catch (Exception $e) {
            return [
                'total_usuarios' => 0,
                'total_medicamentos' => 0,
                'monthly_stats' => $this->getEmptyMonthlyStats()
            ];
        }
    }
    
    private function getMonthlyStats() {
        try {
            $usuario = new Usuario();
            $medicamento = new Medicamento();
            
            $currentYear = date('Y');
            
            $months = [];
            $userCounts = [];
            $medicationCounts = [];
            
            $monthNames = [
                'Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun',
                'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'
            ];
            
            for ($i = 1; $i <= 12; $i++) {
                $month = str_pad($i, 2, '0', STR_PAD_LEFT);
                $startDate = "{$currentYear}-{$month}-01";
                $endDate = date('Y-m-t', strtotime($startDate));
                
                $userCount = $usuario->getCountByDateRange($startDate, $endDate);
                
                $medicationCount = $medicamento->getCountByDateRange($startDate, $endDate);
                
                $months[] = $monthNames[$i - 1];
                $userCounts[] = $userCount;
                $medicationCounts[] = $medicationCount;
            }
            
            return [
                'months' => $months,
                'users' => $userCounts,
                'medications' => $medicationCounts
            ];
            
        } catch (Exception $e) {
            return $this->getEmptyMonthlyStats();
        }
    }
    
    private function getEmptyMonthlyStats() {
        return [
            'months' => ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
            'users' => array_fill(0, 12, 0),
            'medications' => array_fill(0, 12, 0)
        ];
    }
}

?>
