.sidebar {
    min-height: 100vh;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.25rem 0;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: #212529;
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1.2rem;
}

.main-content {
    min-height: 100vh;
}

.navbar-brand {
    font-weight: bold;
}

.user-info {
    font-size: 0.9rem;
}

.dataTables_wrapper .dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 1rem;
}

.table-responsive {
    border-radius: 0.375rem;
}

.badge {
    font-size: 0.75rem;
}

.img-thumbnail {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    transition: transform 0.2s;
}

.img-thumbnail:hover {
    transform: scale(1.1);
}

.dataTables_wrapper {
    width: 100%;
    margin: 0;
}

.dataTables_wrapper .dataTables_scroll {
    width: 100%;
}

.dataTables_wrapper .dataTables_scrollBody {
    width: 100%;
}

.dataTables_wrapper .dataTables_scrollHead {
    width: 100%;
}

.dataTables_wrapper .dataTables_scrollHeadInner {
    width: 100%;
}

.dataTables_wrapper .dataTables_scrollHeadInner table {
    width: 100% !important;
}

#medicamentosTable {
    width: 100% !important;
    margin: 0;
}

.table-responsive {
    width: 100%;
    overflow-x: auto;
    margin: 0;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 0.5rem;
}

.dataTables_wrapper .row {
    margin: 0;
}

.dataTables_wrapper .col-sm-12,
.dataTables_wrapper .col-md-6,
.dataTables_wrapper .col-md-5,
.dataTables_wrapper .col-md-7 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

#medicamentosTable th,
#medicamentosTable td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#medicamentosTable th:nth-child(1),
#medicamentosTable td:nth-child(1) {
    width: 8%;
    min-width: 60px;
}

#medicamentosTable th:nth-child(2),
#medicamentosTable td:nth-child(2) {
    width: 20%;
    min-width: 150px;
    white-space: normal;
}

#medicamentosTable th:nth-child(3),
#medicamentosTable td:nth-child(3) {
    width: 15%;
    min-width: 120px;
}

#medicamentosTable th:nth-child(4),
#medicamentosTable td:nth-child(4) {
    width: 15%;
    min-width: 120px;
}

#medicamentosTable th:nth-child(5),
#medicamentosTable td:nth-child(5) {
    width: 12%;
    min-width: 80px;
}

#medicamentosTable th:nth-child(6),
#medicamentosTable td:nth-child(6) {
    width: 10%;
    min-width: 70px;
    text-align: center;
}

#medicamentosTable th:nth-child(7),
#medicamentosTable td:nth-child(7) {
    width: 12%;
    min-width: 100px;
}

#medicamentosTable th:nth-child(8),
#medicamentosTable td:nth-child(8) {
    width: 8%;
    min-width: 120px;
    text-align: center;
}
