<?php

require_once __DIR__ . '/../config/api_client.php';
require_once __DIR__ . '/../config/constants.php';

class Usuario {
    private $api;

    public $id_usuario;
    public $nombre;
    public $apellido_paterno;
    public $apellido_materno;
    public $correo;
    public $password;
    public $telefono;
    public $fecha_nacimiento;
    public $estado_auditoria;
    public $fecha_creacion;

    public function __construct() {
        $this->api = new ApiClient();
    }
    
    public function create() {
        $data = [
            'nombre' => htmlspecialchars(strip_tags($this->nombre)),
            'apellidoPaterno' => htmlspecialchars(strip_tags($this->apellido_paterno)),
            'apellidoMaterno' => htmlspecialchars(strip_tags($this->apellido_materno)),
            'correo' => htmlspecialchars(strip_tags($this->correo)),
            'password' => $this->password,
            'telefono' => htmlspecialchars(strip_tags($this->telefono)),
            'fechaNacimiento' => $this->fecha_nacimiento
        ];

        $response = $this->api->post('usuarios', $data);

        if ($response['success']) {
            return $response['data']['data']['idUsuario'] ?? true;
        }

        return false;
    }
    
    public function read() {
        $response = $this->api->get('usuarios');

        if ($response['success']) {
            return $response['data']['data'] ?? [];
        }

        return [];
    }
    
    public function getCountByDateRange($startDate, $endDate) {
        $usuarios = $this->read();
        $count = 0;
        
        foreach ($usuarios as $usuario) {
            $fechaCreacion = $usuario['fechaCreacion'] ?? null;
            if ($fechaCreacion && $fechaCreacion >= $startDate && $fechaCreacion <= $endDate) {
                $count++;
            }
        }
        
        return $count;
    }
    
    public function readOne() {
        $response = $this->api->get('usuarios/' . $this->id_usuario);

        if ($response['success'] && isset($response['data']['data'])) {
            $row = $response['data']['data'];
            $this->nombre = $row['nombre'];
            $this->apellido_paterno = $row['apellidoPaterno'];
            $this->apellido_materno = $row['apellidoMaterno'];
            $this->correo = $row['correo'];
            $this->telefono = $row['telefono'];
            $this->fecha_nacimiento = $row['fechaNacimiento'];
            $this->estado_auditoria = $row['estadoAuditoria'];
            $this->fecha_creacion = $row['fechaCreacion'];
            return true;
        }

        return false;
    }
    
    public function update() {
        $data = [
            'nombre' => htmlspecialchars(strip_tags($this->nombre)),
            'apellidoPaterno' => htmlspecialchars(strip_tags($this->apellido_paterno)),
            'apellidoMaterno' => htmlspecialchars(strip_tags($this->apellido_materno)),
            'correo' => htmlspecialchars(strip_tags($this->correo)),
            'telefono' => htmlspecialchars(strip_tags($this->telefono)),
            'fechaNacimiento' => $this->fecha_nacimiento
        ];

        if (!empty($this->password)) {
            $data['password'] = $this->password;
        }

        $response = $this->api->put('usuarios/' . $this->id_usuario, $data);

        return $response['success'];
    }
    
    public function delete() {
        $response = $this->api->delete('usuarios/' . $this->id_usuario);

        return $response['success'];
    }

    public function emailExists() {
        $usuarios = $this->read();

        foreach ($usuarios as $usuario) {
            if ($usuario['correo'] === $this->correo &&
                (!isset($this->id_usuario) || $usuario['idUsuario'] != $this->id_usuario)) {
                return true;
            }
        }

        return false;
    }

    public function authenticateUser($email, $password) {
        $usuarios = $this->read();

        foreach ($usuarios as $usuario) {
            if ($usuario['correo'] === $email && $usuario['estadoAuditoria'] == ESTADO_ACTIVO) {
                return $usuario;
            }
        }

        return false;
    }
}

?>
