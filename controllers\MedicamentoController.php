<?php

require_once __DIR__ . '/../models/Medicamento.php';
require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../config/functions.php';

class MedicamentoController {
    private $medicamento;
    
    public function __construct() {
        $this->medicamento = new Medicamento();
    }
    
    public function index() {
        Auth::requireAuth();

        if ($this->isAjaxRequest() && isset($_GET['draw'])) {
            $this->getDataTablesData();
            return;
        }
        $medicamentos = [];
        include __DIR__ . '/../views/medicamentos/index.php';
    }

    public function getDataTablesData() {
        Auth::requireAuth();

        $draw = intval($_GET['draw'] ?? 1);
        $start = intval($_GET['start'] ?? 0);
        $length = intval($_GET['length'] ?? 25);
        $search = $_GET['search']['value'] ?? '';
        $orderColumn = intval($_GET['order'][0]['column'] ?? 0);
        $orderDir = $_GET['order'][0]['dir'] ?? 'desc';

        $columns = ['idMedicamento', 'nombre', 'tipo_medicamento_nombre', 'presentacion_medicamento_nombre', 'dosis_cantidad', 'imagenUrl', 'fechaCreacion'];
        $orderBy = $columns[$orderColumn] ?? 'idMedicamento';

        $result = $this->medicamento->readPaginated($start, $length, $search, $orderBy, $orderDir);
        $data = [];
        foreach ($result['data'] as $med) {
            $medicamento_formatted = [
                'id_medicamento' => $med['idMedicamento'],
                'nombre' => $med['nombre'],
                'imagen_url' => $med['imagenUrl'],
                'dosis_cantidad' => $med['dosis_cantidad'] ?? $med['dosisCantidad'] ?? 0,
                'estado_auditoria' => $med['estadoAuditoria'],
                'fecha_creacion' => $med['fechaCreacion'],
                'tipo_medicamento_nombre' => $med['tipoMedicamento']['nombre'] ?? null,
                'presentacion_medicamento_nombre' => $med['presentacionMedicamento']['nombre'] ?? null,
                'unidad_dosis_nombre' => $med['unidadDosis']['nombre'] ?? null,
                'unidad_dosis_abreviatura' => $med['unidadDosis']['abreviatura'] ?? null
            ];

            $data[] = [
                $medicamento_formatted['id_medicamento'],
                '<strong>' . htmlspecialchars($medicamento_formatted['nombre']) . '</strong>',
                !empty($medicamento_formatted['tipo_medicamento_nombre']) ?
                    '<span class="badge bg-info">' . htmlspecialchars($medicamento_formatted['tipo_medicamento_nombre']) . '</span>' :
                    '<span class="text-muted">No especificado</span>',
                !empty($medicamento_formatted['presentacion_medicamento_nombre']) ?
                    '<span class="badge bg-secondary">' . htmlspecialchars($medicamento_formatted['presentacion_medicamento_nombre']) . '</span>' :
                    '<span class="text-muted">No especificado</span>',
                (isset($medicamento_formatted['dosis_cantidad']) && $medicamento_formatted['dosis_cantidad'] >= 0) ?
                    '<strong>' . htmlspecialchars($medicamento_formatted['dosis_cantidad']) . '</strong>' .
                    (!empty($medicamento_formatted['unidad_dosis_abreviatura']) ? ' ' . htmlspecialchars($medicamento_formatted['unidad_dosis_abreviatura']) : '') :
                    '<span class="text-muted">No especificado</span>',
                !empty($medicamento_formatted['imagen_url']) ?
                    '<img src="' . htmlspecialchars($medicamento_formatted['imagen_url']) . '" alt="Imagen del medicamento" class="img-thumbnail" style="max-width: 50px; max-height: 50px;">' :
                    '<i class="bi bi-image text-muted fs-4"></i>',
                '<small class="text-muted">' . date('d/m/Y H:i', strtotime($medicamento_formatted['fecha_creacion'])) . '</small>',
                '<div class="btn-group" role="group">
                    <a href="' . BASE_URL . 'medicamentos/show/' . $medicamento_formatted['id_medicamento'] . '" class="btn btn-sm btn-outline-info" title="Ver detalles">
                        <i class="bi bi-eye"></i>
                    </a>
                    <a href="' . BASE_URL . 'medicamentos/edit/' . $medicamento_formatted['id_medicamento'] . '" class="btn btn-sm btn-outline-warning" title="Editar">
                        <i class="bi bi-pencil"></i>
                    </a>
                    <a href="javascript:void(0);" onclick="confirmDeleteLink(\'' . BASE_URL . 'medicamentos/delete/' . $medicamento_formatted['id_medicamento'] . '\')" class="btn btn-sm btn-outline-danger" title="Eliminar">
                        <i class="bi bi-trash"></i>
                    </a>
                </div>'
            ];
        }

        header('Content-Type: application/json');
        echo json_encode([
            'draw' => $draw,
            'recordsTotal' => $result['totalRecords'],
            'recordsFiltered' => $result['filteredRecords'],
            'data' => $data
        ]);
    }
    
    public function show($id) {
        Auth::requireAuth();
        
        $this->medicamento->id_medicamento = $id;
        $medicamento_data = $this->medicamento->readOne();
        
        if ($medicamento_data) {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'data' => $medicamento_data]);
            } else {
                include __DIR__ . '/../views/medicamentos/show.php';
            }
        } else {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Medicamento no encontrado']);
            } else {
                header('Location: ' . BASE_URL . 'medicamentos');
            }
        }
    }
    
    public function create() {
    Auth::requireAuth();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $this->medicamento->nombre = $_POST['nombre'] ?? '';
        $this->medicamento->imagen_url = $_POST['imagen_url'] ?? '';
        $this->medicamento->id_tipo_medicamento = $_POST['id_tipo_medicamento'] ?? null;
        $this->medicamento->id_presentacion_medicamento = $_POST['id_presentacion_medicamento'] ?? null;
        $this->medicamento->id_unidad_dosis = $_POST['id_unidad_dosis'] ?? null;
        $this->medicamento->dosis_cantidad = $_POST['dosis_cantidad'] ?? 0;
        $this->medicamento->estado_auditoria = ESTADO_ACTIVO;
        
        if ($this->medicamento->create()) {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => MSG_CREATED_SUCCESS]);
            } else {
                redirectWithSuccess(BASE_URL . 'medicamentos', MSG_CREATED_SUCCESS);
            }
        } else {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => MSG_ERROR_GENERAL]);
            } else {
                redirectWithError(BASE_URL . 'medicamentos/create', MSG_ERROR_GENERAL);
            }
        }
    } else {
        $tipos_medicamento = $this->medicamento->getTiposMedicamento();
        $presentaciones_medicamento = $this->medicamento->getPresentacionesMedicamento();
        $unidades_dosis = $this->medicamento->getUnidadesDosis();
        
        include __DIR__ . '/../views/medicamentos/create.php';
    }
}

public function edit($id) {
    Auth::requireAuth();
    
    $this->medicamento->id_medicamento = $id;
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $this->medicamento->nombre = $_POST['nombre'] ?? '';
        $this->medicamento->imagen_url = $_POST['imagen_url'] ?? '';
        $this->medicamento->id_tipo_medicamento = $_POST['id_tipo_medicamento'] ?? null;
        $this->medicamento->id_presentacion_medicamento = $_POST['id_presentacion_medicamento'] ?? null;
        $this->medicamento->id_unidad_dosis = $_POST['id_unidad_dosis'] ?? null;
        $this->medicamento->dosis_cantidad = $_POST['dosis_cantidad'] ?? 0;
        
        if ($this->medicamento->update()) {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => MSG_UPDATED_SUCCESS]);
            } else {
                redirectWithSuccess(BASE_URL . 'medicamentos', MSG_UPDATED_SUCCESS);
            }
        } else {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => MSG_ERROR_GENERAL]);
            } else {
                redirectWithError(BASE_URL . 'medicamentos/edit/' . $id, MSG_ERROR_GENERAL);
            }
        }
    } else {
        $medicamento_data = $this->medicamento->readOne();
        if ($medicamento_data) {
            $tipos_medicamento = $this->medicamento->getTiposMedicamento();
            $presentaciones_medicamento = $this->medicamento->getPresentacionesMedicamento();
            $unidades_dosis = $this->medicamento->getUnidadesDosis();
            
            include __DIR__ . '/../views/medicamentos/edit.php';
        } else {
            header('Location: ' . BASE_URL . 'medicamentos');
        }
    }
}

public function delete($id) {
    Auth::requireAuth();
    
    $this->medicamento->id_medicamento = $id;
    
    if ($this->medicamento->delete()) {
        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => MSG_DELETED_SUCCESS]);
        } else {
            redirectWithSuccess(BASE_URL . 'medicamentos', MSG_DELETED_SUCCESS);
        }
    } else {
        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => MSG_ERROR_GENERAL]);
        } else {
            redirectWithError(BASE_URL . 'medicamentos', MSG_ERROR_GENERAL);
        }
    }
}
    
    public function getCatalogos() {
        Auth::requireAuth();
        
        $tipos_medicamento = $this->medicamento->getTiposMedicamento();
        $presentaciones_medicamento = $this->medicamento->getPresentacionesMedicamento();
        $unidades_dosis = $this->medicamento->getUnidadesDosis();
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => [
                'tipos_medicamento' => $tipos_medicamento,
                'presentaciones_medicamento' => $presentaciones_medicamento,
                'unidades_dosis' => $unidades_dosis
            ]
        ]);
    }
    
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

?>
