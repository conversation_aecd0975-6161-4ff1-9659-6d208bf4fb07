document.addEventListener("DOMContentLoaded", function() {
    const togglePassword = document.getElementById("togglePassword");
    
    if (togglePassword) {
        togglePassword.addEventListener("click", function() {
            const password = document.getElementById("password");
            const icon = this.querySelector("i");
            
            if (password.type === "password") {
                password.type = "text";
                icon.classList.remove("bi-eye");
                icon.classList.add("bi-eye-slash");
            } else {
                password.type = "password";
                icon.classList.remove("bi-eye-slash");
                icon.classList.add("bi-eye");
            }
        });
    }
});

document.addEventListener("DOMContentLoaded", function() {
    const createForm = document.getElementById("createUserForm");
    
    if (createForm) {
        createForm.addEventListener("submit", function(e) {
            const password = document.getElementById("password").value;
            
            if (password.length < 6) {
                e.preventDefault();
                alert("La contraseña debe tener al menos 6 caracteres");
                return false;
            }
            
            const submitBtn = this.querySelector("button[type=\"submit\"]");
            submitBtn.innerHTML = "<i class=\"bi bi-hourglass-split me-2\"></i>Creando...";
            submitBtn.disabled = true;
        });
    }
});

document.addEventListener("DOMContentLoaded", function() {
    const editForm = document.getElementById("editUserForm");
    
    if (editForm) {
        editForm.addEventListener("submit", function(e) {
            const password = document.getElementById("password").value;
            
            if (password && password.length < 6) {
                e.preventDefault();
                alert("La contraseña debe tener al menos 6 caracteres");
                return false;
            }
            
            const submitBtn = this.querySelector("button[type=\"submit\"]");
            submitBtn.innerHTML = "<i class=\"bi bi-hourglass-split me-2\"></i>Actualizando...";
            submitBtn.disabled = true;
        });
    }
});
