<?php
$page_title = 'Detalles del Medicamento';
$current_page = 'medicamentos';
$page_actions = '<a href="' . BASE_URL . 'medicamentos" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Volver</a>';

ob_start();
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-capsule me-2"></i>
                    Información del Medicamento
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (!empty($medicamento_data['imagen_url'])): ?>
                        <div class="col-md-4 mb-3 text-center">
                            <label class="form-label fw-bold">
                                <i class="bi bi-image me-1"></i>
                                Imagen:
                            </label>
                            <div>
                                <img src="<?php echo htmlspecialchars($medicamento_data['imagen_url']); ?>"
                                    alt="<?php echo htmlspecialchars($medicamento_data['nombre']); ?>"
                                    class="img-thumbnail"
                                    style="max-width: 200px; max-height: 200px;">
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="<?php echo !empty($medicamento_data['imagen_url']) ? 'col-md-8' : 'col-md-12'; ?>">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-capsule me-1"></i>
                                    Nombre del Medicamento:
                                </label>
                                <p class="form-control-plaintext fs-5 fw-bold text-primary">
                                    <?php echo htmlspecialchars($medicamento_data['nombre']); ?>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-tags me-1"></i>
                                    Tipo de Medicamento:
                                </label>
                                <p class="form-control-plaintext">
                                    <?php if (!empty($medicamento_data['tipo_medicamento_nombre'])): ?>
                                        <span class="badge bg-info fs-6">
                                            <?php echo htmlspecialchars($medicamento_data['tipo_medicamento_nombre']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">No especificado</span>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-box me-1"></i>
                                    Presentación:
                                </label>
                                <p class="form-control-plaintext">
                                    <?php if (!empty($medicamento_data['presentacion_medicamento_nombre'])): ?>
                                        <span class="badge bg-secondary fs-6">
                                            <?php echo htmlspecialchars($medicamento_data['presentacion_medicamento_nombre']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">No especificado</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-calculator me-1"></i>
                                    Dosis:
                                </label>
                                <p class="form-control-plaintext">
                                    <?php if (isset($medicamento_data['dosis_cantidad']) && $medicamento_data['dosis_cantidad'] > 0): ?>
                                        <span class="fs-5 fw-bold">
                                            <?php echo htmlspecialchars($medicamento_data['dosis_cantidad']); ?>
                                            <?php if (!empty($medicamento_data['unidad_dosis_abreviatura'])): ?>
                                                <?php echo htmlspecialchars($medicamento_data['unidad_dosis_abreviatura']); ?>
                                            <?php endif; ?>
                                        </span>
                                        <?php if (!empty($medicamento_data['unidad_dosis_nombre'])): ?>
                                            <br><small class="text-muted">
                                                (<?php echo htmlspecialchars($medicamento_data['unidad_dosis_nombre']); ?>)
                                            </small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">No especificado</span>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Estado:
                                </label>
                                <p class="form-control-plaintext">
                                    <?php if ($medicamento_data['estado_auditoria'] == ESTADO_ACTIVO): ?>
                                        <span class="badge bg-success fs-6">Activo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger fs-6">Inactivo</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-clock me-1"></i>
                            Fecha de Registro:
                        </label>
                        <p class="form-control-plaintext">
                            <?php echo date('d/m/Y H:i:s', strtotime($medicamento_data['fecha_creacion'])); ?>
                        </p>
                    </div>

                    <?php if (!empty($medicamento_data['imagen_url'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="bi bi-link me-1"></i>
                                URL de la Imagen:
                            </label>
                            <p class="form-control-plaintext">
                                <a href="<?php echo htmlspecialchars($medicamento_data['imagen_url']); ?>"
                                    target="_blank"
                                    class="text-break">
                                    <?php echo htmlspecialchars($medicamento_data['imagen_url']); ?>
                                    <i class="bi bi-box-arrow-up-right ms-1"></i>
                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <a href="<?php echo BASE_URL; ?>medicamentos" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        Volver a la lista
                    </a>
                    <div>
                        <a href="<?php echo BASE_URL; ?>medicamentos/edit/<?php echo $medicamento_data['id_medicamento']; ?>"
                            class="btn btn-warning me-2">
                            <i class="bi bi-pencil me-2"></i>
                            Editar
                        </a>
                        <a href="javascript:void(0);"
                            class="btn btn-danger"
                            onclick="confirmDeleteLink('<?php echo BASE_URL; ?>medicamentos/delete/<?php echo $medicamento_data['id_medicamento']; ?>')">
                            <i class="bi bi-trash me-2"></i>
                            Eliminar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>