<?php

function redirectWithSuccess($url, $messageKey) {
    $_SESSION['flash_messages']['success'] = $messageKey;
    header('Location: ' . $url);
    exit();
}

function redirectWithError($url, $messageKey) {
    $_SESSION['flash_messages']['error'] = $messageKey;
    header('Location: ' . $url);
    exit();
}

function displayFlashMessages() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (isset($_SESSION['flash_messages'])) {
        $messages = $_SESSION['flash_messages'];

        $messageToPass = null;

        if (isset($messages['success'])) {
            $messageToPass = [
                'type' => 'success',
                'text' => $messages['success']
            ];
        } elseif (isset($messages['error'])) {
            $messageToPass = [
                'type' => 'error',
                'text' => $messages['error']
            ];
        }

        if ($messageToPass) {
            $json = json_encode($messageToPass);
            $base64 = base64_encode($json);

            echo "<script>sessionStorage.setItem('flash_message', '{$base64}');</script>";
        }

        unset($_SESSION['flash_messages']);
    }
}
