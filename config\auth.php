<?php

require_once 'constants.php';

class Auth {
    
    public static function generateToken($user_data) {
        $header = json_encode(['typ' => 'JWT', 'alg' => JWT_ALGORITHM]);
        $payload = json_encode([
            'user_id' => $user_data['id'] ?? 0,
            'email' => $user_data['email'],
            'exp' => time() + JWT_EXPIRATION,
            'iat' => time()
        ]);
        
        $headerEncoded = self::base64UrlEncode($header);
        $payloadEncoded = self::base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, JWT_SECRET, true);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    public static function validateToken($token) {
        if (!$token) return false;
        
        $parts = explode('.', $token);
        if (count($parts) !== 3) return false;
        
        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, JWT_SECRET, true);
        $signatureCheck = self::base64UrlEncode($signature);
        
        if ($signatureCheck !== $signatureEncoded) return false;
        
        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        
        if ($payload['exp'] < time()) return false;
        
        return $payload;
    }
    
    public static function isAuthenticated() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION['auth_token'])) {
            $payload = self::validateToken($_SESSION['auth_token']);
            if ($payload) {
                return $payload;
            }
        }
        
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $token = str_replace('Bearer ', '', $headers['Authorization']);
            $payload = self::validateToken($token);
            if ($payload) {
                return $payload;
            }
        }
        
        return false;
    }
    
    public static function requireAuth() {
        $user = self::isAuthenticated();
        if (!$user) {
            if (self::isAjaxRequest()) {
                http_response_code(401);
                echo json_encode(['error' => MSG_ACCESS_DENIED]);
                exit;
            } else {
                header('Location: ' . BASE_URL . 'login');
                exit;
            }
        }
        return $user;
    }
    
    public static function logout() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        session_destroy();
        header('Location: ' . BASE_URL . 'login');
        exit;
    }
    
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    private static function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
    
    private static function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

?>
