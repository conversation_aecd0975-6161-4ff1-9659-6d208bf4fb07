<?php

require_once __DIR__ . '/../models/Usuario.php';
require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../config/functions.php';

class UsuarioController {
    private $usuario;
    
    public function __construct() {
        $this->usuario = new Usuario();
    }
    
    public function index() {
        Auth::requireAuth();

        $usuarios = $this->usuario->read();

        if ($this->isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'data' => $usuarios]);
        } else {
            include __DIR__ . '/../views/usuarios/index.php';
        }
    }
    
    public function show($id) {
        Auth::requireAuth();
        
        $this->usuario->id_usuario = $id;
        
        if ($this->usuario->readOne()) {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'data' => $this->usuario]);
            } else {
                include __DIR__ . '/../views/usuarios/show.php';
            }
        } else {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Usuario no encontrado']);
            } else {
                header('Location: ' . BASE_URL . 'usuarios');
            }
        }
    }
    
    public function create() {
        Auth::requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->usuario->nombre = $_POST['nombre'] ?? '';
            $this->usuario->apellido_paterno = $_POST['apellido_paterno'] ?? '';
            $this->usuario->apellido_materno = $_POST['apellido_materno'] ?? '';
            $this->usuario->correo = $_POST['correo'] ?? '';
            $this->usuario->password = $_POST['password'] ?? '';
            $this->usuario->telefono = $_POST['telefono'] ?? '';
            $this->usuario->fecha_nacimiento = $_POST['fecha_nacimiento'] ?? '';
            $this->usuario->estado_auditoria = ESTADO_ACTIVO;
            
            if ($this->usuario->emailExists()) {
                if ($this->isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'El correo electrónico ya está registrado']);
                } else {
                    redirectWithError(BASE_URL . 'usuarios/create', 'El correo electrónico ya está registrado');
                }
                return;
            }
            
            if ($this->usuario->create()) {
                if ($this->isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true, 'message' => MSG_CREATED_SUCCESS]);
                } else {
                    redirectWithSuccess(BASE_URL . 'usuarios', MSG_CREATED_SUCCESS);
                }
            } else {
                if ($this->isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => MSG_ERROR_GENERAL]);
                } else {
                    redirectWithError(BASE_URL . 'usuarios/create', MSG_ERROR_GENERAL);
                }
            }
        } else {
            include __DIR__ . '/../views/usuarios/create.php';
        }
    }
    
    public function edit($id) {
        Auth::requireAuth();
        
        $this->usuario->id_usuario = $id;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->usuario->nombre = $_POST['nombre'] ?? '';
            $this->usuario->apellido_paterno = $_POST['apellido_paterno'] ?? '';
            $this->usuario->apellido_materno = $_POST['apellido_materno'] ?? '';
            $this->usuario->correo = $_POST['correo'] ?? '';
            $this->usuario->password = $_POST['password'] ?? '';
            $this->usuario->telefono = $_POST['telefono'] ?? '';
            $this->usuario->fecha_nacimiento = $_POST['fecha_nacimiento'] ?? '';
            
            if ($this->usuario->emailExists()) {
                if ($this->isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'El correo electrónico ya está registrado']);
                } else {
                    redirectWithError(BASE_URL . 'usuarios/edit/' . $id, 'El correo electrónico ya está registrado');
                }
                return;
            }
            
            if ($this->usuario->update()) {
                if ($this->isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true, 'message' => MSG_UPDATED_SUCCESS]);
                } else {
                    redirectWithSuccess(BASE_URL . 'usuarios', MSG_UPDATED_SUCCESS);
                }
            } else {
                if ($this->isAjaxRequest()) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => MSG_ERROR_GENERAL]);
                } else {
                    redirectWithError(BASE_URL . 'usuarios/edit/' . $id, MSG_ERROR_GENERAL);
                }
            }
        } else {
            if ($this->usuario->readOne()) {
                include __DIR__ . '/../views/usuarios/edit.php';
            } else {
                header('Location: ' . BASE_URL . 'usuarios');
            }
        }
    }
    
    public function delete($id) {
        Auth::requireAuth();
        
        $this->usuario->id_usuario = $id;
        
        if ($this->usuario->delete()) {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => MSG_DELETED_SUCCESS]);
            } else {
                redirectWithSuccess(BASE_URL . 'usuarios', MSG_DELETED_SUCCESS);
            }
        } else {
            if ($this->isAjaxRequest()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => MSG_ERROR_GENERAL]);
            } else {
                redirectWithError(BASE_URL . 'usuarios', MSG_ERROR_GENERAL);
            }
        }
    }
    
    private function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
?>
